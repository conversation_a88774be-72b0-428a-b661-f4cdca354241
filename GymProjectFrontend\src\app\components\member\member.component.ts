import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { Member } from '../../models/member';

import { MemberService } from '../../services/member.service';
import { ToastrService } from 'ngx-toastr';
import { faEdit, faTrashAlt, faInfoCircle, faSyncAlt, faUserPlus, faComment } from '@fortawesome/free-solid-svg-icons';
import { MatDialog } from '@angular/material/dialog';
import { MemberUpdateComponent } from '../crud/member-update/member-update.component';
import { MemberDetailDialogComponent } from '../member-detail-dialog/member-detail-dialog.component';
import { Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';

import { Router } from '@angular/router';
import { FileUploadService } from '../../services/file-upload.service';

@Component({
    selector: 'app-member',
    templateUrl: './member.component.html',
    styleUrls: ['./member.component.css'],
    standalone: false
})
export class MemberComponent implements OnInit, OnDestroy {
  members: Member[] = [];
  currentPage = 1;
  totalPages = 0;
  totalItems = 0;
  totalRegisteredMembers = 0; // Toplam kayıtlı üye sayısı
  searchText = '';
  isLoading = false;
  viewMode = 'table'; // 'table' or 'card'
  selectedGender: number | null = null; // Cinsiyet filtresi için eklendi (null = Tümü)
  genderFilterDisabled = false; // Cinsiyet filtresi spam koruması

  // Icons
  faTrashAlt = faTrashAlt;
  faEdit = faEdit;
  faInfoCircle = faInfoCircle;
  faSyncAlt = faSyncAlt;
  faUserPlus = faUserPlus;
  faComment = faComment;



  private searchSubject = new Subject<string>();
  private destroy$ = new Subject<void>();

  constructor(
    private memberService: MemberService,
    private toastrService: ToastrService,
    public dialog: MatDialog,
    private router: Router,
    private fileUploadService: FileUploadService
  ) {
    this.searchSubject.pipe(
      debounceTime(750),
      takeUntil(this.destroy$)
    ).subscribe(searchValue => {
      this.searchText = searchValue;
      this.currentPage = 1;
      // Arama yapıldığında cinsiyet filtresi korunur
      this.loadMembers();
    });
  }

  ngOnInit(): void {
    // Başlangıçta tüm üyeleri yükle (filtresiz)
    this.loadMembers();
    // Toplam kayıtlı üye sayısını yükle
    this.loadTotalRegisteredMembers();
    // loadStatistics metodu kaldırıldı

    // Load saved view mode preference
    const savedViewMode = localStorage.getItem('memberViewMode');
    if (savedViewMode) {
      this.viewMode = savedViewMode;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // loadStatistics metodu kaldırıldı

  loadTotalRegisteredMembers(): void {
    this.memberService.getTotalRegisteredMembers().subscribe({
      next: (response) => {
        if (response.success) {
          this.totalRegisteredMembers = response.data;
        }
      },
      error: (error) => {
        console.error('Error fetching total registered members:', error);
      }
    });
  }



  refreshMembers() {
    // Yenileme yapıldığında mevcut filtreler korunur
    this.loadMembers();
    // Toplam kayıtlı üye sayısını yenile
    this.loadTotalRegisteredMembers();
    // loadStatistics çağrısı kaldırıldı
    this.toastrService.info('Üye listesi yenilendi', 'Bilgi');
  }

  loadMembers() {
    this.isLoading = true;
    // Profil resmi hata listesini temizle (yeniden deneme için)
    this.imageErrors.clear();

    // Servis çağrısına selectedGender parametresini ekle
    this.memberService.getAllPaginated(this.currentPage, this.searchText, this.selectedGender).subscribe({
      next: (response) => {
        if (response.success) {
          // Eski yapıya geri dönüldü
          this.members = response.data.data;
          this.totalPages = response.data.totalPages;
          this.totalItems = response.data.totalCount;
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error fetching members:', error);
        this.toastrService.error('Üyeler yüklenirken bir hata oluştu.', 'Hata');
        this.isLoading = false;
      }
    });
  }

  clearSearch() {
    this.searchText = '';
    this.currentPage = 1;
    // Arama temizlendiğinde cinsiyet filtresi korunur
    this.loadMembers();
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      // Sayfa değiştiğinde mevcut filtreler korunur
      this.loadMembers();
    }
  }

  getPaginationRange(): number[] {
    const range = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, this.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(this.totalPages, startPage + maxPagesToShow - 1);

    if (endPage - startPage + 1 < maxPagesToShow) {
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      range.push(i);
    }

    return range;
  }

  onSearch(event: any): void {
    const searchValue = event.target.value;
    this.searchSubject.next(searchValue);
  }

  // Avatar and status helpers
  getInitials(name: string): string {
    if (!name) return '';

    const nameParts = name.split(' ');
    if (nameParts.length === 1) return nameParts[0].charAt(0).toUpperCase();

    return (nameParts[0].charAt(0) + nameParts[nameParts.length - 1].charAt(0)).toUpperCase();
  }

  getAvatarColor(name: string): string {
    if (!name) return '#4361ee';

    // Generate a consistent color based on the name
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    const colors = [
      '#4361ee', '#3f37c9', '#4895ef', '#4cc9f0',
      '#560bad', '#7209b7', '#b5179e', '#f72585',
      '#3a0ca3', '#4361ee', '#4895ef', '#4cc9f0'
    ];

    return colors[Math.abs(hash) % colors.length];
  }

  getMemberStatus(member: Member): string {
    // This is a placeholder - implement based on your actual member status logic
    if (member.isActive) return 'Aktif';
    return 'Pasif';
  }

  getMemberStatusClass(member: Member): string {
    // This is a placeholder - implement based on your actual member status logic
    if (member.isActive) return 'status-active';
    return 'status-expired';
  }

  viewMemberDetails(member: Member): void {
    // Open member details in dialog
    const dialogRef = this.dialog.open(MemberDetailDialogComponent, {
      width: '95%',
      maxWidth: '1000px',
      height: '85vh',
      maxHeight: '750px',
      data: { memberId: member.memberID },
      panelClass: 'member-detail-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      // Dialog kapandığında herhangi bir işlem yapmaya gerek yok
      // Sayfa scroll pozisyonu korunacak
    });
  }

  onDelete(member: Member): void {
    this.dialogService.confirmMemberDelete(member.name, member).subscribe(result => {
      if (result) {
        this.memberService.delete(member.memberID).subscribe({
          next: (response) => {
            if (response.success) {
              this.toastrService.success('Üye başarıyla silindi');
              this.loadMembers();
            }
          },
          error: (error) => {
            this.toastrService.error('Üye silinirken bir hata oluştu');
          }
        });
      }
    });
  }

  openUpdateDialog(member: Member): void {
    const dialogRef = this.dialog.open(MemberUpdateComponent, {
      maxWidth: '100vw',
      maxHeight: '100vh',
      data: member
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadMembers();
      }
    });
  }

  openWhatsApp(phoneNumber: string): void {
    // Format the phone number for WhatsApp
    // Remove any spaces, dashes, parentheses
    let formattedNumber = phoneNumber.replace(/[\s\-\(\)]/g, '');

    // If the number doesn't start with '+' or country code, add Turkish country code
    if (!formattedNumber.startsWith('+') && !formattedNumber.startsWith('90')) {
      formattedNumber = '90' + formattedNumber;
    }

    // If the number starts with '0', remove it and add country code
    if (formattedNumber.startsWith('0')) {
      formattedNumber = '9' + formattedNumber;
    }

    // Create WhatsApp URL
    const whatsappUrl = `https://wa.me/${formattedNumber}`;

    // Open in a new tab
    window.open(whatsappUrl, '_blank');
  }

  // Cinsiyet filtresi değiştiğinde tetiklenir
  onGenderFilterChange(selectedGenderValue: number | null): void {
    if (this.selectedGender !== selectedGenderValue && !this.genderFilterDisabled) {
      this.genderFilterDisabled = true; // Butonları devre dışı bırak
      this.selectedGender = selectedGenderValue;
      this.currentPage = 1; // Filtre değiştiğinde ilk sayfaya dön
      this.loadMembers();

      // 1 saniye sonra butonları tekrar aktif et
      setTimeout(() => {
        this.genderFilterDisabled = false;
      }, 1000);
    }
  }

  // Cinsiyet bilgisine göre avatar rengi döndürür
  getGenderColor(gender: number): string {
    return gender === 1 ? '#4361ee' : '#FF69B4'; // Erkek: Mavi, Kadın: Pembe (MemberFilter ile aynı)
  }

  // View mode değiştirme ve localStorage'a kaydetme
  setViewMode(mode: 'table' | 'card'): void {
    this.viewMode = mode;
    localStorage.setItem('memberViewMode', mode);
  }

  // Profil fotoğrafı URL'ini döndürür
  getProfileImageUrl(member: Member): string | null {
    // Sadece userID varsa ve daha önce hata almamışsa URL döndür
    if (member.userID && !this.hasImageError(member.userID)) {
      return this.fileUploadService.getProfileImageUrl(member.userID);
    }
    return null;
  }

  // Profil resmi hata durumlarını takip etmek için
  private imageErrors = new Set<number>();

  private hasImageError(userId: number): boolean {
    return this.imageErrors.has(userId);
  }

  // Profil fotoğrafı yükleme hatası durumunda çağrılır
  onProfileImageError(event: any): void {
    // Hata durumunda image'ı gizle, icon gösterilsin
    event.target.style.display = 'none';

    // Parent element'e error class ekle
    const parentElement = event.target.closest('.avatar-circle, .avatar-circle-lg');
    if (parentElement) {
      parentElement.classList.add('image-error');
    }

    // UserID'yi hata listesine ekle (src'den çıkar)
    const src = event.target.src;
    if (src) {
      const userIdMatch = src.match(/profile-image\/(\d+)/);
      if (userIdMatch) {
        const userId = parseInt(userIdMatch[1]);
        this.imageErrors.add(userId);
      }
    }
  }

  // Profil fotoğrafı başarıyla yüklendiğinde çağrılır
  onProfileImageLoad(event: any): void {
    // Loading class'ını kaldır
    event.target.classList.remove('profile-image-loading');
  }
}
